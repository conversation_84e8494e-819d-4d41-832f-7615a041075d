import pandas as pd

def process_files():
    print("=" * 50)
    print("开始处理文件...")
    print("=" * 50)

    # 读取第一个文件 file_gpt.xlsx
    print("📖 正在读取 file_gpt.xlsx...")
    df_gpt = pd.read_excel('file_gpt.xlsx', header=1)  # 从第二行开始读取数据
    print(f"✅ file_gpt.xlsx 读取完成，共 {len(df_gpt)} 条记录")

    # 初始化结果DataFrame
    matched_df = pd.DataFrame()  # 匹配的数据存入a.xlsx
    unmatched_df = pd.DataFrame()  # 不匹配的数据存入b.xlsx

    # 处理第二个文件 file_nas.xlsx
    print("\n📖 正在读取 file_nas.xlsx...")
    df_nas = pd.read_excel('file_nas.xlsx', header=1)
    print(f"✅ file_nas.xlsx 读取完成，共 {len(df_nas)} 条记录")
    nas_matched = set()  # 记录已匹配的file_nas行

    # 处理第三个文件 file_ys.xlsx
    print("\n📖 正在读取 file_ys.xlsx...")
    df_ys = pd.read_excel('file_ys.xlsx', header=1)
    print(f"✅ file_ys.xlsx 读取完成，共 {len(df_ys)} 条记录")
    ys_matched = set()  # 记录已匹配的file_ys行
    
    # 处理file_nas匹配逻辑
    print("\n" + "=" * 50)
    print("🔄 开始处理 file_nas 数据匹配...")
    print("=" * 50)
    nas_total = len(df_nas)
    nas_matched_count = 0

    for nas_idx, nas_row in df_nas.iterrows():
        nas_col1_value = nas_row.iloc[0]  # 第一列数据
        nas_col1_values = str(nas_col1_value).split('&') if '&' in str(nas_col1_value) else [str(nas_col1_value)]
        nas_col1_values = [v.strip() for v in nas_col1_values]
        
        matched = False
        for idx, gpt_row in df_gpt.iterrows():
            gpt_col6_value = gpt_row.iloc[5]  # 第六列数据
            gpt_col6_values = str(gpt_col6_value).split('/') if '/' in str(gpt_col6_value) else [str(gpt_col6_value)]
            gpt_col6_values = [v.strip() for v in gpt_col6_values]
            
            # 检查是否有交集
            if set(nas_col1_values) & set(gpt_col6_values):
                matched = True
                nas_matched_count += 1
                nas_matched.add(nas_idx)
                # 合并数据
                new_row = gpt_row.to_dict()
                new_row.update({
                    '产品型号': nas_row.iloc[0],
                    '地址': nas_row.iloc[2],
                    '来源': 'file_nas'
                })
                matched_df = pd.concat([matched_df, pd.DataFrame([new_row])], ignore_index=True)
                break  # 找到匹配后跳出内层循环

        # 显示进度
        current_progress = nas_idx + 1
        if current_progress % 10 == 0 or current_progress == nas_total:
            print(f"📊 file_nas 进度: {current_progress}/{nas_total} ({current_progress/nas_total*100:.1f}%) - 已匹配: {nas_matched_count}")

        if not matched:
            new_unmatched_row = {
                '产品型号': nas_row.iloc[0],
                '地址': nas_row.iloc[2],
                '来源': 'file_nas_unmatched'
            }
            unmatched_df = pd.concat([unmatched_df, pd.DataFrame([new_unmatched_row])], ignore_index=True)

    print(f"\n✅ file_nas 处理完成！匹配: {nas_matched_count}, 未匹配: {nas_total - nas_matched_count}")

    # 处理file_ys匹配逻辑
    print("\n" + "=" * 50)
    print("🔄 开始处理 file_ys 数据匹配...")
    print("=" * 50)
    ys_total = len(df_ys)
    ys_matched_count = 0

    for ys_idx, ys_row in df_ys.iterrows():
        ys_col3_value = str(ys_row.iloc[2]).strip()  # 第三列数据
        
        matched = False
        for idx, gpt_row in df_gpt.iterrows():
            gpt_col6_value = gpt_row.iloc[5]  # 第六列数据
            gpt_col6_values = str(gpt_col6_value).split('/') if '/' in str(gpt_col6_value) else [str(gpt_col6_value)]
            gpt_col6_values = [v.strip() for v in gpt_col6_values]
            
            if ys_col3_value in gpt_col6_values:
                matched = True
                ys_matched_count += 1
                ys_matched.add(ys_idx)
                # 合并数据
                new_row = gpt_row.to_dict()
                new_row.update({
                    '产品型号': ys_row.iloc[2],
                    '地址': ys_row.iloc[10],
                    '来源': 'file_ys'
                })
                matched_df = pd.concat([matched_df, pd.DataFrame([new_row])], ignore_index=True)
                break  # 找到匹配后跳出内层循环

        # 显示进度
        current_progress = ys_idx + 1
        if current_progress % 10 == 0 or current_progress == ys_total:
            print(f"📊 file_ys 进度: {current_progress}/{ys_total} ({current_progress/ys_total*100:.1f}%) - 已匹配: {ys_matched_count}")

        if not matched:
            new_unmatched_row = {
                '产品型号': ys_row.iloc[2],
                '地址': ys_row.iloc[10],
                '来源': 'file_ys_unmatched'
            }
            unmatched_df = pd.concat([unmatched_df, pd.DataFrame([new_unmatched_row])], ignore_index=True)

    print(f"\n✅ file_ys 处理完成！匹配: {ys_matched_count}, 未匹配: {ys_total - ys_matched_count}")

    # 保存结果到不同文件
    print("\n" + "=" * 50)
    print("💾 正在保存结果文件...")
    print("=" * 50)

    print("📝 正在保存 a.xlsx (匹配数据)...")
    matched_df.to_excel('a.xlsx', index=False)
    print(f"✅ a.xlsx 保存完成")

    print("📝 正在保存 b.xlsx (未匹配数据)...")
    unmatched_df.to_excel('b.xlsx', index=False)
    print(f"✅ b.xlsx 保存完成")

    # 最终统计
    print("\n" + "=" * 50)
    print("🎉 所有处理完成！")
    print("=" * 50)
    print(f"📊 处理统计:")
    print(f"   • file_gpt 总记录数: {len(df_gpt)}")
    print(f"   • file_nas 总记录数: {len(df_nas)} (匹配: {nas_matched_count}, 未匹配: {len(df_nas) - nas_matched_count})")
    print(f"   • file_ys 总记录数: {len(df_ys)} (匹配: {ys_matched_count}, 未匹配: {len(df_ys) - ys_matched_count})")
    print(f"📁 输出文件:")
    print(f"   • a.xlsx: {len(matched_df)} 条匹配记录")
    print(f"   • b.xlsx: {len(unmatched_df)} 条未匹配记录")
    print("=" * 50)

if __name__ == "__main__":
    process_files()