import os
from openpyxl import Workbook
from openpyxl.styles import Font

def get_directory_structure(rootdir):
    """
    递归获取目录结构，过滤空文件夹和指定文件类型
    返回格式: [(文件夹层级列表, 文件名), ...]
    过滤条件: 排除 .jpg、.png、.db 文件和空文件夹
    """
    dir_structure = []
    max_depth = 0

    # 定义要过滤的文件扩展名
    filtered_extensions = {'.jpg', '.png', '.db'}

    for dirpath, _, filenames in os.walk(rootdir):
        # 过滤掉指定扩展名的文件
        valid_files = []
        for file in filenames:
            file_ext = os.path.splitext(file)[1].lower()
            if file_ext not in filtered_extensions:
                valid_files.append(file)

        # 只处理包含有效文件的文件夹（跳过空文件夹）
        if valid_files:
            # 获取相对路径并拆分为各级文件夹
            relative_path = os.path.relpath(dirpath, rootdir)
            if relative_path == '.':
                folders = [os.path.basename(rootdir)]
            else:
                folders = [os.path.basename(rootdir)] + relative_path.split(os.sep)

            # 更新最大深度
            if len(folders) > max_depth:
                max_depth = len(folders)

            # 记录有效文件
            for file in valid_files:
                dir_structure.append((folders, file))

    return dir_structure, max_depth

def write_to_excel(data, max_depth, output_file):
    """
    将目录结构写入Excel文件，文件夹层级按顺序分列
    """
    wb = Workbook()
    ws = wb.active
    ws.title = "目录结构"
    
    # 设置标题行
    headers = [f"文件夹层级 {i+1}" for i in range(max_depth)] + ["文件名"]
    for col, header in enumerate(headers, 1):
        ws.cell(row=1, column=col, value=header).font = Font(bold=True)
    
    # 写入数据
    for row_idx, (folders, filename) in enumerate(data, 2):
        # 写入文件夹层级
        for col_idx, folder in enumerate(folders, 1):
            ws.cell(row=row_idx, column=col_idx, value=folder)
        # 写入文件名
        ws.cell(row=row_idx, column=max_depth+1, value=filename)
    
    # 调整列宽
    for col in range(1, max_depth + 2):
        ws.column_dimensions[chr(64 + col)].width = 30
    
    wb.save(output_file)
    print(f"目录结构已成功写入到 {output_file}")

if __name__ == "__main__":
    # 设置要扫描的文件夹路径
    folder_path = "./AI客服知识库"
    
    # 验证路径是否存在
    while not os.path.isdir(folder_path):
        print("错误: 指定的路径不存在或不是一个文件夹")
        folder_path = input("请重新输入要扫描的文件夹路径: ").strip()
    
    # 设置输出Excel文件名
    output_filename = "directory_structure_split.xlsx"
    
    # 获取目录结构
    structure, max_depth = get_directory_structure(folder_path)
    
    # 写入Excel
    write_to_excel(structure, max_depth, output_filename)